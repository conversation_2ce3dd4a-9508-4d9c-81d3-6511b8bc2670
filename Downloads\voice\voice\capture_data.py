import cv2
import mediapipe as mp
import csv
import os
import time

# Ask for the label
label = input("Enter the label for this sign (e.g., Hello, Yes, No): ")

# Create data folder if it doesn’t exist
os.makedirs("data", exist_ok=True)
csv_path = os.path.join("data", "sign_data.csv")

# If file doesn't exist, write header
if not os.path.exists(csv_path):
    with open(csv_path, mode='w', newline='') as f:
        writer = csv.writer(f)
        header = []
        for i in range(21):  # 21 landmarks
            header += [f"x{i}", f"y{i}"]
        header.append("label")
        writer.writerow(header)

# Mediapipe setup
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=False, max_num_hands=1, min_detection_confidence=0.7)
mp_draw = mp.solutions.drawing_utils

cap = cv2.VideoCapture(0)

samples_to_collect = 50   # Number of samples per sign
delay_between_samples = 1 # Seconds between samples
collected = 0

print(f"Collecting {samples_to_collect} samples for label: {label}")
time.sleep(2)  # Give time to get ready

while collected < samples_to_collect:
    ret, frame = cap.read()
    if not ret:
        break

    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = hands.process(rgb_frame)

    if results.multi_hand_landmarks:
        for hand_landmarks in results.multi_hand_landmarks:
            mp_draw.draw_landmarks(frame, hand_landmarks, mp_hands.HAND_CONNECTIONS)

            # Save landmark data
            row = []
            for lm in hand_landmarks.landmark:
                row += [lm.x, lm.y]
            row.append(label)

            with open(csv_path, mode='a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(row)

            collected += 1
            print(f"Saved sample {collected}/{samples_to_collect}")
            time.sleep(delay_between_samples)

    cv2.imshow("Hand Data Capture", frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()

print(f"✅ Done collecting {samples_to_collect} samples for label: {label}")
