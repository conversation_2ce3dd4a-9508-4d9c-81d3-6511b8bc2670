import cv2
import mediapipe as mp
import numpy as np
import tensorflow as tf

# Load model & labels
model = tf.keras.models.load_model("models/sign_language_model.h5")
labels = np.load("models/labels.npy", allow_pickle=True)


# Mediapipe setup
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=False, max_num_hands=1, min_detection_confidence=0.7)
mp_draw = mp.solutions.drawing_utils

cap = cv2.VideoCapture(0)

while True:
    ret, frame = cap.read()
    if not ret:
        break

    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = hands.process(rgb_frame)

    prediction_text = "No hand"

    if results.multi_hand_landmarks:
        for hand_landmarks in results.multi_hand_landmarks:
            mp_draw.draw_landmarks(frame, hand_landmarks, mp_hands.HAND_CONNECTIONS)

            # Extract landmarks
            row = []
            for lm in hand_landmarks.landmark:
                row += [lm.x, lm.y]
            row = np.array(row).reshape(1, -1)

            # Predict
            prediction = model.predict(row, verbose=0)
            class_id = np.argmax(prediction)
            prediction_text = labels[class_id]

    # Show prediction
    cv2.putText(frame, str(prediction_text), (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    cv2.imshow("Sign Language Prediction", frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
